(function($) {
  /**
   * polyfill for html5 form attr
   */

  // detect if browser supports this
  var sampleElement = $('[form]').get(0);
  var isIE11 = !(window.ActiveXObject) && "ActiveXObject" in window;
  if (sampleElement && window.HTMLFormElement && (sampleElement.form instanceof HTMLFormElement || sampleElement instanceof window.HTMLFormElement) && !isIE11) {
    // browser supports it, no need to fix
    return;
  }

  /**
   * Append a field to a form
   *
   */
  $.fn.appendField = function(data) {
    // for form only
    if (!this.is('form')) return;

    // wrap data
    if (!$.isArray(data) && data.name && data.value) {
      data = [data];
    }

    var $form = this;

    // attach new params
    $.each(data, function(i, item) {
      $('<input/>')
        .attr('type', 'hidden')
        .attr('name', item.name)
        .val(item.value).appendTo($form);
    });

    return $form;
  };

  /**
   * Find all input fields with form attribute point to jQuery object
   * 
   */
  $('form[id]').submit(function(e) {
    // serialize data
    var data = $('[form='+ this.id + ']').serializeArray();
    // append data to form
    $(this).appendField(data);
  }).each(function() {
    var form = this,
      $fields = $('[form=' + this.id + ']');

    $fields.filter('button, input').filter('[type=reset],[type=submit]').click(function() {
      var type = this.type.toLowerCase();
      if (type === 'reset') {
        // reset form
        form.reset();
        // for elements outside form
        $fields.each(function() {
          this.value = this.defaultValue;
          this.checked = this.defaultChecked;
        }).filter('select').each(function() {
          $(this).find('option').each(function() {
            this.selected = this.defaultSelected;
          });
        });
      } else if (type.match(/^submit|image$/i)) {
        $(form).appendField({name: this.name, value: this.value}).submit();
      }
    });
  });


})(jQuery);

/*************************************************
	DATEPICKER
*************************************************/

$(document).on('focus',".datepicker", function(){
	$(this).datepicker({
		dateFormat: 'yy-mm-dd',
		onSelect: function() {
			$(this).removeAttr('data-date');
		},
		selectMultiple:true
	});
});

/*************************************************
	ICON PICKER FOR PAGES
*************************************************/

$(document).on('focus',".iconpicker", function(){
	$(this).iconpicker({
		templates: {
			search:'<input type="search" class="form-control iconpicker-search" placeholder="Sök och filtrera" />'
		}
	});
});

/*************************************************
	FORM BUILDER - UNSET RADIO FIELDS
*************************************************/

$(document).on('mouseup','form.form-builder .radio input', function() {
	$(this).data('previous',$(this).prop('checked'));
}).on('click','form.form-builder .radio input', function() {
	var $this = $(this);
	if( $this.is(':checked') == $this.data('previous') ) {
		$this.prop('checked', false);
	}
});

$(document).on('mouseup','form.form-builder .radio label', function() {
	var $this = $(this).children('input');
	$this.data('previous',$this.prop('checked'));
}).on('click','form.form-builder .radio label', function() {
	var $this = $(this).children('input');
	if( $this.is(':checked') == $this.data('previous') ) {
		$this.prop('checked', false);
	}
});

/*************************************************
	RESIZE MENU
*************************************************/

// Set constant element widths and run resize
var resizeMenu = new Array(), resizeTimer, outerWidth;

// Function to handle resize of menu
function resize() {
	var $html = '',
			menuResizable = $("#navbar-collapse>ul"),
			resizable = menuResizable.find(">li.dropdown");

	resizeMenu['total'] = $('.main-header').outerWidth();
	resizeMenu['resizable'] = 0;
	resizeMenu['move'] = new Array();
	
	menuResizable.find(">li:not(.dropdown)").each(function(i) {
		var $this = $(this);
		resizeMenu['resizable'] += Math.ceil($this.outerWidth());
		
		if (resizeMenu['total'] < resizeMenu['constant'] + resizeMenu['resizable'] + 120) {
			$this.css({display:"none"});
			resizeMenu['move'].push( $this.html());
		} else {
			$this.css({display:"block"});
		}
	});

	if( resizeMenu['move'].length ) {
		$html += '<li class="dropdown"><a href="#" class="dropdown-toggle" data-toggle="dropdown">';
		$html += '<i class="fa fa-bars"></i></a><ul class="dropdown-menu" role="menu">'
		resizeMenu['move'].forEach(function(e) {
			$html += '<li>' + e + '</li>';
		});
		$html += '</ul></li>';
		
		resizable.remove();
		menuResizable.append($html);
	} else {
		resizable.remove();
	}
}

$(window).on('load',function() {
	outerWidth = $(window).outerWidth();
	if(outerWidth > 768) {
		resizeMenu['constant'] = Math.ceil($('.logo').outerWidth() + $('.navbar-custom-menu').outerWidth());
		resize();
	}
});

// Monitoring size of browser window and run resize
$(window).resize(function() {
	if(outerWidth > 768) {
		clearTimeout(resizeTimer);
		resizeTimer = setTimeout(resize, 100);
	}
});

$(document).ready(function() {
	/*************************************************
		GENERAL
	*************************************************/
	
	// $('.datepicker').datepicker();
	
	$('.decrementDateByOneMonth').on('click',function(e) {
		e.preventDefault();
		var button = $(this),
			input  = button.parents('.date').find('input.datepicker');
			if( isValidDate(input.val()) )
				var newDate = new Date(input.val());
			else
				var newDate = new Date();

			newDate.setMonth(newDate.getMonth() - 1);
			input.val(newDate.yyyymmdd());
	});
	
	$('.incrementDateByOneMonth').on('click',function(e) {
		e.preventDefault();
		var button = $(this),
			input  = button.parents('.date').find('input.datepicker');
			if( isValidDate(input.val()) )
				var newDate = new Date(input.val());
			else
				var newDate = new Date();

			newDate.setMonth(newDate.getMonth() + 1);
			input.val(newDate.yyyymmdd());
	});
	
	$('.setTodaysDate').on('click',function(e) {
		e.preventDefault();
		var button = $(this),
			input  = button.parents('.date').find('input.datepicker');
			var newDate = new Date();
			input.val(newDate.yyyymmdd());
	});
	
	$('.decrementDateByYear').on('click',function(e) {
		e.preventDefault();
		var button = $(this),
			buttonValue = parseInt(button.attr('data-time')),
			input = button.parents('.date').find('input.datepicker'),
			decrementFrom = input.attr('data-date');
		if( Number.isInteger(buttonValue) ) {
			if( isValidDate(decrementFrom) ) {
				var newDate = new Date(decrementFrom);
				input.removeAttr('data-date');
			}
			else if( isValidDate(input.val()) )
				var newDate = new Date(input.val());
			else
				var newDate = new Date();
			
			newDate.setYear(newDate.getFullYear() - buttonValue);
			input.val(newDate.yyyymmdd());
		}
	});
	
	$('.incrementDateByYear').on('click',function(e) {
		e.preventDefault();
		var button = $(this),
			buttonValue = parseInt(button.attr('data-time')),
			input = button.parents('.date').find('input.datepicker'),
			incrementFrom = input.attr('data-date');
		if( Number.isInteger(buttonValue) ) {
			if( isValidDate(incrementFrom) ) {
				var newDate = new Date(incrementFrom);
				input.removeAttr('data-date');
			}
			else if( isValidDate(input.val()) )
				var newDate = new Date(input.val());
			else
				var newDate = new Date();
			
			newDate.setYear(newDate.getFullYear() + buttonValue);
			input.val(newDate.yyyymmdd());
		}
	});
	
	$('.tableToggle').on('click',function(e) {
		$(this).next().toggleClass('tableToggleHidden');
	});
	
	/*************************************************
		TREE
	*************************************************/
	var tree = $('.document-tree li'),
		treeLinks = $('.document-tree li > span'),
		treeMarkAllButton = $('.document-tree .markAll');
	
	tree.each(function(){
		var den = $(this);
		if(den.children('ul').length > 0) {
			den.addClass('parent');
		}
	});
	
	treeLinks.click(function(){
		var target = $(this);
		target.parent().toggleClass('active');
		target.parent().children('ul').slideToggle('fast');
	});
	
	treeMarkAllButton.click(function(e){
		
		if($(e.target).is(':checked')) {
			var checked = true;
		} else {
			var checked = false;
		}
		
		$(this).closest('li').children().find('input').prop('checked',checked);
		
	});

	/*************************************************
		TASKS
	*************************************************/
	
	$('.changeTaskRecurrecne').on('change',function(e) {
		e.preventDefault();
		var $this	= $(this),
		finishDate	= $('.changeTaskFinishDate');
		if($this.val() != 0) {
			finishDate.val('0000-00-00');
			finishDate.hide();
		} else {
			finishDate.show();
		}
	});

	$('.changeTaskGoalType').on('change',function(e) {
		e.preventDefault();
		var $this	= $(this),
		monitoring	= $('.taskMonitoring'),
		achievement	= $('.taskAchievement');
		if($this.val() == 0) {
			achievement.show();
			monitoring.hide();
		} else {
			achievement.hide();
			monitoring.show();
		}
	});
	
	/*************************************************
		DOCUMENTS
	*************************************************/
	
	$('.changeDocumentType').on('change',function(e) {
		e.preventDefault();
		var $this	= $(this),
			$rev	= $('#documents_valid_until'),
			$update	= $('#documents_reminder'),
			$valid	= $('.changeDocumentTypeDisplay');
		if($this.val() == 1 || $this.val() == 2) {
			$rev.val('0000-00-00');
			$update.val(0);
			$valid.hide();
		} else {
			if($rev.val() == '' || $rev.val() == '0000-00-00') {
				var newDate = new Date();
				newDate.setYear(newDate.getFullYear() + 1);
				$rev.val(newDate.yyyymmdd());
			}
			if($update.val() == 0) {
				$update.val(1);
			}
			$valid.show();
		}
	});
	
	/*************************************************
		DELETE UPLOAD
	*************************************************/
	
	$('.dialog-delete').on('click',function() {
		var $dialogDelete = $(this);
		$( $dialogDelete.attr('data-dialog') ).dialog({
			resizable: false,
			height:140,
			width: 400,
			modal: true,
			title: $dialogDelete.attr('data-title'),
			buttons: {
				"Ta bort ": function() {
					$.ajax({
						dataType: "json",
						type: "POST",
						url: $dialogDelete.attr('data-url'),
						data: {
							id: $dialogDelete.attr('data-id'),
							kvalprakcsrf: $('meta[name="kvalprakcsrf"]').attr('content'),
							uuid_kvalprak: $('input[name="uuid_kvalprak"]').val()
						},
						cache: false
					}).done(function( d ) {
						if(d.result) {
							$dialogDelete.closest('tr').fadeOut(600, function() {
								$(this).remove();
							});
						}
					});
					$( this ).dialog( "close" );
				},
				"Avbryt": function() {
					$( this ).dialog( "close" );
				}
			}
		});
	});
	
	/*************************************************
		CONFIRM DIALOG
	*************************************************/
	$('.dialog-confirm').on('click',function() {
		var $dialogConfirm = $(this);
		$( $dialogConfirm.attr('data-dialog') ).dialog({
			resizable: false,
			// height:140,
			width: 400,
			modal: true,
			// title: $dialogConfirm.attr('data-title'),
			buttons: {
				"Ja": function() {
					$.ajax({
						dataType: "json",
						type: "POST",
						url: $dialogConfirm.attr('data-url'),
						data: {
							id: $dialogConfirm.attr('data-id'),
							other: $dialogConfirm.attr('data-other'),
							redirect: $dialogConfirm.attr('data-redirect'),
							kvalprakcsrf: $('meta[name="kvalprakcsrf"]').attr('content')
						},
						cache: false
					}).done(function( d ) {
						if( d.result ) {
							if( d.href ) {
								window.location.href = d.href.endsWith("user/documents") ? d.href + "#draft" : d.href;
							}
							else {
								location.reload();
							}
						}
					});
					$( this ).dialog( "close" );
				},
				"Nej": function() {
					$( this ).dialog( "close" );
				}
			}
		});
	});
	
	/*************************************************
		RISK ASSESMENTS
	*************************************************/
	
	var riskDropdownOptions	= $("#hiddenRisks").html(),
		valueToText = {
			0:['',''],
			1:['',''],
			2:['Låg','riskok'],
			3:['Låg','riskok'],
			4:['Måttlig','riskwarning'],
			5:['Måttlig','riskwarning'],
			6:['Allvarligt','riskdanger'],
			7:['Mycket allvarligt','riskmeltdown'],
			8:['Mycket allvarligt','riskmeltdown']
		};
	
	$(document).on('click','.addRisks', function() {
		var clicked = $(this);
		var riskCount = $('.risk-item').length + 1;

		// Check if we're using the new modern design
		if ($('.risks-container').length > 0) {
			var riskHtml = `
				<div class="risk-item risks">
					<div class="risk-header">
						<h6 class="risk-title">Risk ${riskCount}</h6>
						<button type="button" class="btn-remove-risk removeRisk" onclick="$(this).closest('.risk-item').remove(); updateRisksTabel();">
							<i class="fa fa-times"></i>
						</button>
					</div>
					${riskDropdownOptions}
				</div>
			`;
			$('.risks-container').append(riskHtml);
		} else {
			// Fallback to old design
			clicked.parents('.box').after('<div class="risks box">' +
				'<div class="box-header with-border"> ' +
				`<h6>Risk ${$('.risks .riskName').length + 1}</h6> ` +
				 '<div class="box-tools float-right">' +
					'<button type="button" class="btn btn-box-tool removeRisk removeButton"><i class="fa fa-times"></i></button>' +
				'</div></div>' +
			riskDropdownOptions +
			'</div>');
		}
		updateRisksTabel();
	});
	
	$(document).on('click','.removeRisk', function() {
		var $removeRisk = $(this);
		$removeRisk.parents('.box').fadeOut(600, function() {
			$(this).remove();
			updateRisksTabel();
		});
	});
	
	function updateRisksTabel() {
		var risks = [],
			riskName = [],
			riskOccurrence = [],
			riskOccurrenceName = [],
			riskSeverity = [],
			riskSeverityName = [],
			riskAcceptable = [],
			riskResponsibleName = [],
			riskDone = [],
			riskFinished = [],
			html = '';
		$('.risks .riskName').each(function(i, v) {
			riskName.push(v.value);
		});
		$('.risks .riskOccurrence').each(function(i, v) {
			riskOccurrence.push(Number(v.value));
			riskOccurrenceName.push($('option:selected',v).text().replace(/\(.*\)/g, ''));
		});
		$('.risks .riskSeverity').each(function(i, v) {
			riskSeverity.push(Number(v.value));
			riskSeverityName.push($('option:selected',v).text().replace(/\(.*\)/g, ''));
		});
		// $('.risks .riskResponsible').each(function(i, v) {
			// if(v.value != 0)
				// riskResponsibleName.push($('option:selected',v).text());
			// else
				// riskResponsibleName.push('');
		// });
		$('.risks .riskDone').each(function(i, v) {
			riskDone.push(v.value);
		});
		$('.risks .riskAcceptable').each(function(i, v) {
			if($(this).is(':checked'))
				riskAcceptable.push('Ja')
			else
				riskAcceptable.push('Nej')
		});
		$('.risks .riskFinished').each(function(i, v) {
			riskFinished.push(v.value);
		});

		$.each(riskName, function( i, v ) {
			var sum = riskOccurrence[i]+riskSeverity[i];
			if(sum >= 2 && v.length >= 1) {
				html +=
				'<tr class="' + valueToText[sum][1] + '">' +
				'<td>' + v + '</td>' +
				'<td>' + riskOccurrenceName[i] + '</td>' +
				'<td>' + riskSeverityName[i] + '</td>' +
				'<td>' + valueToText[sum][0] + '</td>' +
				// '<td>' + riskResponsibleName[i] + '</td>' +
				'<td>' + riskDone[i] + '</td>' +
				'<td>' + riskAcceptable[i] + '</td>' +
				'<td>' + riskFinished[i] + '</td>' +
				'</tr>';
			}
		});
		if(html.length > 0) {
			$('#risks').show();
		} else {
			$('#risks').hide();
		}
		$("#risks tbody").html(html);

		// Update risk scores in modern design
		$('.risk-item').each(function(index) {
			var occurrence = $(this).find('.riskOccurrence').val();
			var severity = $(this).find('.riskSeverity').val();
			var sum = Number(occurrence) + Number(severity);
			var scoreElement = $(this).find('.form-control-static span');

			if (sum >= 2 && occurrence && severity) {
				var scoreText = valueToText[sum] ? valueToText[sum][0] : '';
				var scoreClass = valueToText[sum] ? valueToText[sum][1] : '';

				scoreElement.removeClass('badge-warning badge-success badge-danger badge-secondary')
					.addClass('badge-' + (scoreClass === 'riskok' ? 'success' :
						scoreClass === 'riskwarning' ? 'warning' :
						scoreClass === 'riskdanger' ? 'danger' :
						scoreClass === 'riskmeltdown' ? 'danger' : 'secondary'))
					.text(scoreText || 'Moderate');
			} else {
				scoreElement.removeClass('badge-warning badge-success badge-danger badge-secondary')
					.addClass('badge-secondary').text('Not calculated');
			}
		});
	}
	
	$(document).on('change','.risks',function() {
		updateRisksTabel();
	});
	
	/*************************************************
		EVENT ANALYSIS
	*************************************************/
	
	var eventDropdownOptions		= $("#hiddenEvent").html(),
		eventDropdownOptionsChild	= $("#hiddenEventChild").html(),
		eventDropdownOptionsEdit	= $("#hiddenEventEditChild").html(),
		eventDropdownOptionsAl		= $("#hiddenActionList").html(),
		eventIdI = 0,
		eventIdChildI = 0,
		eventIdEditChildI = 0;
	
	$(document).on('click','.addQa', function() {
		var clicked = $(this);
		eventIdI++;

		//Prepend the options and give select a unique name for when fetching data in backend
		clicked.parents('.box').after("<div class='event box padding' data-id='" + eventIdI + "'>" +
			'<div class="event-question-header box-header with-border"> ' + 
			`<h6>Grundfråga ${$('.event .event-question-header').length + 1}</h6> ` +
				'<div class="box-tools float-right">' +
				'<button type="button" class="btn btn-box-tool removeEventParent removeButton"><i class="fa fa-times"></i></button>' +
			'</div></div>' +
		eventDropdownOptions + 
		'<input type="hidden" name="eventId[]" value="' + eventIdI + '" />' +
		'<div class="box-footer">' +
			'<div class="addQaChild cursorPointer addButton"><i class="fa fa-plus-square"></i> Lägg till följdfråga</div>' +
		'</div>' +
		'<div class="events"></div></div>');
	});
	
	$(document).on('click','.addQaChild', function() {
		var clicked = $(this),
			parent = clicked.parent().parent(),
			parentId = parent.attr('data-id');
		eventIdChildI++;
		parent.find('.removeEventParent').hide();
		
		//Prepend the options and give select a unique name for when fetching data in backend
		parent.children('.events').append("<div class='eventChild boxChild box'>" + 
			'<div class="event-followup-question-header box-header with-border"> ' +
			`<h6>Följdfråga ${parent.children('.events').find(".event-followup-question-header").length + 1}</h6> ` + 
				'<div class="box-tools float-right">' +
				'<button type="button" class="btn btn-box-tool removeEvent removeButton"><i class="fa fa-times"></i></button>' +
			'</div></div>' +
		'<input type="hidden" name="eventIdChild[]" value="' + eventIdChildI + '" />' +
		'<input type="hidden" name="eventParent[]" value="' + parentId + '" />' +
		eventDropdownOptionsChild + 
		'</div>');
	});
	
	$(document).on('click','.addQaChildEdit', function() {
		var clicked = $(this),
			parent = clicked.parent().parent(),
			parentId = parent.attr('data-id');
		eventIdEditChildI++;
		
		parent.children('.events').append("<div class='eventChild boxChild box'>" + 
			'<div class="box-header with-border"> <div class="box-tools float-right">' +
				'<button type="button" class="btn btn-box-tool removeEvent removeButton"><i class="fa fa-times"></i></button>' +
			'</div></div>' +
		'<input type="hidden" name="eventIdEditChild[]" value="' + eventIdEditChildI + '" />' +
		'<input type="hidden" name="eventEditParent[]" value="' + parentId + '" />' +
		eventDropdownOptionsEdit + 
		'</div>');
	});
	
	$(document).on('click','.removeEventParent', function() {
		var $removeEventParent = $(this);
		console.log('click');
		$removeEventParent.parents('.event').fadeOut(600, function() {
			$(this).remove();
		});
	});
	
	$(document).on('click','.removeEvent', function() {
		var $removeEvent = $(this);
		$removeEvent.parents('.eventChild').fadeOut(600, function() {
			$(this).remove();
		});
		// @TODO: Add button again if empty
		// if($removeEvent.parents('.events').val() === '') {
			// $removeEvent.parents('.event').find('.removeEventParent').show();
		// }
	});
	
	$(document).on('click','.addAl', function() {
		var clicked = $(this);
		
		clicked.parents('.box').after("<div class='event box padding'>" +
			'<div class="box-header with-border"> <div class="box-tools float-right">' +
				'<button type="button" class="btn btn-box-tool removeEventAl removeButton"><i class="fa fa-times"></i></button>' +
			'</div></div>' +
		eventDropdownOptionsAl + 
		'</div>');
	});
	
	$(document).on('click','.removeEventAl', function() {
		var $removeEventAl = $(this);
		$removeEventAl.parents('.box').fadeOut(600, function() {
			$(this).remove();
		});
	});

	/*************************************************
		SORTABLE LIST
	*************************************************/
	var sortlist   = $('tbody.sortlist'),
		sortlistTr = $('tbody.sortlist tr'),
		save       = $('button#submit_order'),
		uuid       = $('input#uuid_kvalprak').val();
	
	// Return a helper with preserved width of cells
	var fixHelper = function(e, ui) {
		ui.children().each(function() {
			$(this).width($(this).width());
		});
		return ui;
	};
	
	sortlist.sortable({
		helper: fixHelper,
		update: function() {
			save.removeAttr('disabled');
			// sortlistTr.removeClass('odd');
			// $('tbody.sortlist tr:even').addClass('odd');
		},
		placeholder: "ui-state-highlight",
		start: function (e, ui) { 
			// modify ui.placeholder however you like
			ui.placeholder.html('<td colspan="2"> -- Flytta rad --</td>');
		}
	}).disableSelection();
	
	sortlist.delegate('button.add','click',function() {
		var closeTr	= $(this).closest('tr');
		$('<tr></tr>', {
			'id'    : uuid,
			'class' : 'new-row',
			'html'  : '<td colspan="' + closeTr.children().length  + '"><div class="newRowDiv">-- Ny rad --</div></td>'
		}).insertAfter(closeTr);
		$('button.add').remove();
		save.removeAttr('disabled').html('Sortera & Skapa');
	});
	
	save.click(function(e) {
		var order = sortlist.sortable('toArray');
		$('input#sort_order').val(order);
		// e.preventDefault();
	});

	/*************************************************
		DEVIATION
	*************************************************/
	var department = $('input.department:checkbox');
	
	department.on('click', function() {
		department.prop('required', true);
		if(department.is(':checked')){
			department.prop('required', false);
		}
	});

	/*************************************************
		DEVIATION SORTABLE
	*************************************************/
	var fieldWidget		= $('ul.fieldWidget'),
		fieldAvailable	= $('input#fieldAvailableInput'),
		fieldOne		= $('input#fieldOneInput'),
		fieldTwo		= $('input#fieldTwoInput'),
		fieldThree		= $('input#fieldThreeInput');
	fieldWidget.sortable({
		connectWith: "ul.fieldWidget",
		placeholder: "ui-state-highlight",
		stop: function (e, ui) { 
			fieldAvailable.val($('#fieldAvailable').sortable('toArray'));
			fieldOne.val($('#fieldOne').sortable('toArray'));
			fieldTwo.val($('#fieldTwo').sortable('toArray'));
			fieldThree.val($('#fieldThree').sortable('toArray'));
		}
	}).disableSelection();
	
	/*************************************************
		FORM
	*************************************************/
	$('.formUpload').click(function(e) {
		
		var $this   = $(this),
			$id     = $this.attr('data-id'),
			$url    = $this.attr('data-url'),
			$width  = 500,
			$height = 500,
			$left   = parseInt((screen.availWidth/2) - ($width/2)),
			$top    = parseInt((screen.availHeight/2) - ($height/2)),
			$specs  = 'scrollbars=1,width=' + $width +',height=' + $height + ',left=' + $left + ',top=' + $top,
			$window = window.open($url + 'form/formUpload/' + $id, 'popUp', $specs);
			
		$window.onbeforeunload = function() {
			setTimeout(updateForm, 1500);
		}
		
		function updateForm()
		{
			$.ajax({
				dataType: "json",
				type: "POST",
				url: $url + 'form/formUploadFiles/' + $id,
				data: {
					id: $id,
					kvalprakcsrf: $('meta[name="kvalprakcsrf"]').attr('content')
				},
				cache: false
			}).done(function( documents ) {
				var $html = '<table class="table table-bordered">';
				$html += '<tr><th>Dokumentnamn</th><th>Skapat datum</th><th>Senast reviderad</th><th>Gäller t.o.m.</th></tr>';
				$.each( documents, function( document_id, document ) {
					$html += '<tr>';
					$html += '<td><a href="' + $url + 'documents/' + document.document_id + '" target="_blank">' + document.name + '</a></td>';
					$html += '<td>' + document.created + '</td>';
					$html += '<td>' + document.last_revised + '</td>';
					$html += '<td>' + document.valid_until + '</td>';
					$html += '</tr>';
				});
				
				$html += '</table>';
				
				$('#' + $id + ' .formUploadedFiles').html($html);
			}).fail(function( result ) {
				$('#' + $id + ' .formUploadedFiles').html('');
			});
		}
	});
});

Date.prototype.yyyymmdd = function() {
   var yyyy = this.getFullYear().toString();
   var mm = (this.getMonth()+1).toString(); // getMonth() is zero-based
   var dd  = this.getDate().toString();
   return yyyy + '-' + (mm[1]?mm:"0"+mm[0]) + '-' + (dd[1]?dd:"0"+dd[0]); // padding
};

function isValidDate(dateString) {
	if(typeof dateString === 'undefined') return false;
	var regEx = /^\d{4}-\d{2}-\d{2}$/;
	if(!dateString.match(regEx)) return false;  // Invalid format
	var d = new Date(dateString);
	if(Number.isNaN(d.getTime())) return false; // Invalid date
	return d.toISOString().slice(0,10) === dateString;
}

$.datepicker.regional['sv'] = {
  closeText: 'Stäng',
	prevText: '< Föregående',
	nextText: 'Nästa >',
	currentText: 'Nu',
	monthNames: ['Januari','Februari','Mars','April','Maj','Juni','Juli','Augusti','September','Oktober','November','December'],
    monthNamesShort: ['Jan','Feb','Mar','Apr','Maj','Jun','Jul','Aug','Sep','Okt','Nov','Dec'],
	dayNamesShort: ['Sön','Mån','Tis','Ons','Tor','Fre','Lör'],
	dayNames: ['Söndag','Måndag','Tisdag','Onsdag','Torsdag','Fredag','Lördag'],
	dayNamesMin: ['Sö','Må','Ti','On','To','Fr','Lö'],
	weekHeader: 'Не',
	dateFormat: 'yy-mm-dd',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''
};
$.datepicker.setDefaults($.datepicker.regional['sv']);
$.datepicker.setDefaults({
	changeYear: true,
})

// $.timepicker.regional['sv'] = {
	// timeOnlyTitle: 'Välj klockslag',
	// timeText: 'Tid',
	// hourText: 'Timma',
	// minuteText: 'Minut',
	// secondText: 'Sekund',
	// millisecText: 'Millisekund',
	// timezoneText: 'Tidszon',
	// currentText: 'Nu',
	// closeText: 'Stäng',
	// timeFormat: 'HH:mm',
	// amNames: ['AM', 'A'],
	// pmNames: ['PM', 'P'],
	// isRTL: false
// };
// $.timepicker.setDefaults($.timepicker.regional['sv']);