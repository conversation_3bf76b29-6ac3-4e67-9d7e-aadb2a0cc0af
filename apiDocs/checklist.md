# Checklist API Documentation

All endpoints require authentication via the Auth Api.

## Endpoints

### GET /api/checklist//list
Get all available checklists

**Response:**
```json
{
  "checklists": {
    "form_id1": {
      "form_id": "uuid",
      "name": "string",
      "type": "checklist"
    }
  },
  "pages": {
    "page_id1": {
      "page_id": "uuid", 
      "form_id": "uuid",
      "name": "string"
    }
  }
}
```

### GET /api/checklist/{page_id}/submissions
Get all submissions for a checklist page

**Parameters:**
- `page_id` (UUIDv4): The page ID to get submissions for. You will need to find the page that correspond to the user selected form_id. Each form has 1 page only

**Response:**
```json
{
  "submissions": [
    {
      "survey_id": "uuid",
      "page_id": "uuid",
      "survey_date": "YYYY-MM-DD"
    }
  ],
  "users": {
    "user_id1": "User Name",
    "user_id2": "User Name" 
  }
}
```

### GET /api/checklist/{page_id}/questions
Get questions and structure for a checklist page

**Parameters:**
- `page_id` (UUIDv4): The page ID to get questions for

**Response:**
```json
{
  "page": {
    "page_id": "uuid",
    "form_id": "uuid",
    "name": "string"
  },
  "form": {
    "form_id": "uuid",
    "survey_id": "uuid",
    "company_id": "uuid",
    "global": 0|1
  },
  "questions": {
    "question_id1": {
      "question_id": "uuid",
      "page_id": "uuid",
      "type": "string",
      "text": "string"
    }
  },
  "options": {
    "question_id1": {
      "option_id1": {
        "option_id": "uuid",
        "name": "string"
      }
    }
  }
}
```

### POST /api/checklist/{page_id}/submit
Submit a new checklist response

**Parameters:**
- `page_id` (UUIDv4): The page ID to submit to

**Request Body:**
```json
{
  "form_{question_id1}": "answer_value",
  "form_{question_id1}": ["array","of","answers"]
}
```

Multiple answers in case question type requires it

**Response:**
```json
{
  "success": true|false,
  "survey_id": "uuid" // Only on success
}
```

**Error Responses:**
- 400: Invalid request data
- 403: Access denied
- 404: Page not found
- 500: Failed to save checklist
