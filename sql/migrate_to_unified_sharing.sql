-- Migration Script: Convert to Unified Sharing System
-- This script creates the new shares table and migrates data from document_shares

-- Step 1: Create the new unified shares table
CREATE TABLE IF NOT EXISTS `shares` (
  `id` binary(16) NOT NULL,
  `entity_type` varchar(50) NOT NULL,
  `entity_id` binary(16) NOT NULL,
  `shared_by` binary(16) NOT NULL,
  `shared_with` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `status` enum('active','expired','revoked') NOT NULL DEFAULT 'active',
  PRIMARY KEY (`id`),
  KEY `idx_entity` (`entity_type`, `entity_id`),
  KEY `idx_shared_by` (`shared_by`),
  KEY `idx_status` (`status`),
  KEY `idx_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 2: Migrate existing document_shares data to the new shares table
INSERT INTO `shares` (
    `id`, 
    `entity_type`, 
    `entity_id`, 
    `shared_by`, 
    `shared_with`, 
    `created_at`, 
    `expires_at`, 
    `status`
)
SELECT 
    `id`,
    'document' as entity_type,
    `document_id` as entity_id,
    `shared_by`,
    `shared_with`,
    `created_at`,
    `expires_at`,
    `status`
FROM `document_shares`
WHERE NOT EXISTS (
    SELECT 1 FROM `shares` s 
    WHERE s.id = document_shares.id
);

-- Step 3: Verify migration
-- Count records in both tables to ensure migration is complete
SELECT 
    'document_shares' as table_name, 
    COUNT(*) as record_count 
FROM `document_shares`
UNION ALL
SELECT 
    'shares (documents)' as table_name, 
    COUNT(*) as record_count 
FROM `shares` 
WHERE entity_type = 'document';

-- Step 4: Optional - Drop old table after verification
-- WARNING: Only run this after confirming the migration was successful
-- and all functionality is working properly with the new system
-- DROP TABLE IF EXISTS `document_shares`;

-- Step 5: Create indexes for better performance on the new table
ALTER TABLE `shares` 
ADD INDEX `idx_entity_status` (`entity_type`, `entity_id`, `status`),
ADD INDEX `idx_created_at` (`created_at`);

-- Step 6: Add foreign key constraints (optional, depends on your existing schema)
-- Uncomment and modify these if you have proper foreign key relationships
-- ALTER TABLE `shares` 
-- ADD CONSTRAINT `fk_shares_shared_by` 
-- FOREIGN KEY (`shared_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

COMMIT;
