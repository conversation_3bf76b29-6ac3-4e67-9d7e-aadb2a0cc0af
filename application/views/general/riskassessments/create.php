<?php $this->load->view('template/header'); ?>
<style>
.modern-card {
    background: #FFFFFF;
    box-shadow: 0px 3px 5px rgba(9, 30, 66, 0.2), 0px 0px 1px rgba(9, 30, 66, 0.31);
    border-radius: 0.5rem;
    border: none;
    margin-bottom: 1.5rem;
}

.modern-card .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 1rem 1.25rem;
}

.modern-card .card-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #172B4D;
}

.modern-card .card-body {
    padding: 1.25rem;
}

.form-label {
    font-weight: 600;
    color: #172B4D;
    margin-bottom: 0.5rem;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

.badge-tag {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-flex;
    align-items: center;
}

.badge-tag .fa-times {
    margin-left: 0.25rem;
    cursor: pointer;
}

.btn-add-tag {
    background-color: transparent;
    border: 1px dashed #dee2e6;
    color: #6c757d;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
}

.btn-add-tag:hover {
    border-color: #007bff;
    color: #007bff;
}

.risks-container .risk-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.risk-item .risk-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.risk-item .risk-title {
    font-weight: 600;
    color: #172B4D;
}

.btn-remove-risk {
    color: #dc3545;
    background: none;
    border: none;
    padding: 0.25rem;
}

.collapsible-header {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.collapsible-header .fa-chevron-down {
    transition: transform 0.3s ease;
}

.collapsible-header.collapsed .fa-chevron-down {
    transform: rotate(-90deg);
}

.d-flex {
    display: flex !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-center {
    align-items: center !important;
}

.flex-wrap {
    flex-wrap: wrap !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

.text-center {
    text-align: center !important;
}

.text-muted {
    color: #6c757d !important;
}

.btn-link {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
    border: none;
}

.btn-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.05);
}

.form-control:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

select[multiple] {
    height: auto;
}

.float-right {
    float: right !important;
}

.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-success {
    color: #fff;
    background-color: #28a745;
}

.badge-warning {
    color: #212529;
    background-color: #ffc107;
}

.badge-danger {
    color: #fff;
    background-color: #dc3545;
}

.badge-secondary {
    color: #fff;
    background-color: #6c757d;
}

.form-control-static {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
    margin-bottom: 0;
    line-height: 1.5;
    border: solid transparent;
    border-width: 1px 0;
}

.col-md-4, .col-md-6, .col-md-8 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

@media (min-width: 768px) {
    .col-md-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    .col-md-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}
</style>

	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i> Save'
				));
				?>
				<a href="javascript:window.history.go(-1);" title="<?php echo lang('cancel') ?>" class="btn btn-default"><i class="fa fa-reply" aria-hidden="true"></i></a>
			</div>
			<h1>Risk Assessment Heading</h1>
		</section>

		<!-- Main content -->
		<section class="content">
			<div class="row">
				<!-- Main Form Column -->
				<div class="col-md-8">
					<?php
						echo form_open(NULL,array(
							'id' => 'form-company-group',
							'autocomplete' => 'off'
						));
					?>

					<!-- Report Info Card -->
					<div class="modern-card">
						<div class="card-header">
							<h3 class="card-title">Report Info</h3>
						</div>
						<div class="card-body">
							<?php echo validation_errors(); ?>

							<!-- Heading Field -->
							<div class="form-group mb-3">
								<label for="name" class="form-label">Heading</label>
								<p class="form-text">Briefly state what is being reported.</p>
								<input class="form-control required" type="text" name="name" id="name" maxlength="255" value="">
							</div>

							<!-- Case Description Field -->
							<div class="form-group mb-3">
								<label for="description" class="form-label">Case Description</label>
								<p class="form-text">Describe the risk assessment.</p>
								<textarea class="form-control required" rows="5" name="description" id="description"></textarea>
							</div>

							<!-- Date Created Field -->
							<div class="form-group mb-3">
								<label for="date" class="form-label">Date Created</label>
								<p class="form-text">When was the risk assessment created?</p>
								<input class="form-control datepicker" type="text" name="date" id="date" value="<?php echo date('Y-m-d'); ?>">
							</div>

							<!-- Responsible User Field -->
							<?php if($this->auth_kiv && isset($users) && !empty($users)): ?>
							<div class="form-group mb-3">
								<label for="us_id" class="form-label">Responsible User</label>
								<p class="form-text">Who is responsible for the risk assessment?</p>
								<select class="form-control" name="us_id" id="us_id">
									<option value="">Select User</option>
									<?php foreach($users as $user_id => $user_name): ?>
										<option value="<?php echo $user_id; ?>"><?php echo $user_name; ?></option>
									<?php endforeach; ?>
								</select>
							</div>
							<?php endif; ?>

							<!-- Unit Field -->
							<?php if($this->auth_kiv && isset($groups['department']) && !empty($groups['department'])): ?>
							<div class="form-group mb-3">
								<label for="department" class="form-label">Unit</label>
								<p class="form-text">Enter the unit(s) to which the risk assessment concerns.</p>
								<select class="form-control" name="department[]" id="department" multiple>
									<?php foreach($groups['department'] as $dept_id => $dept_name): ?>
										<option value="<?php echo $dept_id; ?>"><?php echo $dept_name; ?></option>
									<?php endforeach; ?>
								</select>
							</div>
							<?php endif; ?>
						</div>
					</div>

					<!-- Risks Table Card -->
					<div class="modern-card">
						<div class="card-header d-flex justify-content-between align-items-center">
							<h3 class="card-title">Risks Table</h3>
							<button type="button" class="btn btn-primary addRisks">
								<i class="fa fa-plus"></i> Add New Risk
							</button>
						</div>
						<div class="card-body">
							<div id="risks" class="table-responsive" style="display:none;">
								<table class="table table-striped risksTabel">
									<thead>
										<tr>
											<th>Risk</th>
											<th>Description</th>
											<th>Frequency</th>
											<th>Severity</th>
											<th>Risk Score</th>
											<th>Who will be affected</th>
											<th>Actions</th>
										</tr>
									</thead>
									<tbody>
										<tr style="display: none;"></tr>
									</tbody>
								</table>
							</div>
							<div class="risks-container">
								<!-- Dynamic risks will be added here -->
							</div>
						</div>
					</div>

					<?php echo form_close(); ?>
				</div>
				<!-- /.col -->

				<!-- Sidebar Column -->
				<div class="col-md-4">
					<!-- Linked Issues and events Card -->
					<div class="modern-card">
						<div class="card-header collapsible-header" data-toggle="collapse" data-target="#linkedIssues">
							<h3 class="card-title">Linked Issues and events</h3>
							<i class="fa fa-chevron-down"></i>
						</div>
						<div class="card-body collapse show" id="linkedIssues">
							<div class="form-group mb-3">
								<label class="form-label">Linked Issue</label>
								<p class="form-text">If this risk assessment is linked to an issue.</p>
								<select class="form-control">
									<option value="">Issue Heading</option>
								</select>
							</div>
						</div>
					</div>

					<!-- Linked Event Analysis Card -->
					<div class="modern-card">
						<div class="card-header">
							<h3 class="card-title">Linked Event Analysis</h3>
						</div>
						<div class="card-body">
							<div class="form-group mb-3">
								<p class="form-text">If this risk assessment is linked to an event analysis.</p>
								<div class="text-center text-muted">
									<p>No event analysis linked</p>
									<button type="button" class="btn btn-link">Connect</button>
								</div>
							</div>
						</div>
					</div>

					<!-- Notifications Card -->
					<div class="modern-card">
						<div class="card-header collapsible-header" data-toggle="collapse" data-target="#notifications">
							<h3 class="card-title">Notifications</h3>
							<i class="fa fa-chevron-down"></i>
						</div>
						<div class="card-body collapse show" id="notifications">
							<!-- Notify about this risk assessment -->
							<div class="form-group mb-3">
								<label class="form-label">Notify about this risk assessment</label>
								<p class="form-text">The following people will receive an email to let them know about this risk assessment.</p>
								<div class="d-flex flex-wrap">
									<span class="badge-tag">Martin <i class="fa fa-times"></i></span>
									<span class="badge-tag">Oskar <i class="fa fa-times"></i></span>
									<span class="badge-tag">Oskar <i class="fa fa-times"></i></span>
									<button type="button" class="btn btn-add-tag">
										<i class="fa fa-plus"></i>
									</button>
								</div>
							</div>

							<!-- Notify about risk assessment report completion -->
							<div class="form-group mb-3">
								<label class="form-label">Notify about risk assessment report completion</label>
								<p class="form-text">The following people will receive an email when the completion of the report.</p>
								<div class="d-flex flex-wrap">
									<span class="badge-tag">Martin <i class="fa fa-times"></i></span>
									<span class="badge-tag">Oskar <i class="fa fa-times"></i></span>
									<span class="badge-tag">Oskar <i class="fa fa-times"></i></span>
									<button type="button" class="btn btn-add-tag">
										<i class="fa fa-plus"></i>
									</button>
								</div>
							</div>

							<!-- Notify about actions completion -->
							<div class="form-group mb-3">
								<label class="form-label">Notify about actions completion</label>
								<p class="form-text">The following people will receive an email when any action has been completed.</p>
								<div class="d-flex flex-wrap">
									<span class="badge-tag">Martin <i class="fa fa-times"></i></span>
									<span class="badge-tag">Oskar <i class="fa fa-times"></i></span>
									<span class="badge-tag">Oskar <i class="fa fa-times"></i></span>
									<button type="button" class="btn btn-add-tag">
										<i class="fa fa-plus"></i>
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /.col -->
			</div>
			<!-- /.row -->
		</section>

		<div id="hiddenRisks" style="display:none">
			<?php
				foreach($risksFields as $key => $val):
					buildInputs($val);
				endforeach;
			?>
		</div>
		<!-- /.content -->
	</div>
	<!-- /.content-wrapper -->

<script>
$(document).ready(function() {
    // Collapsible functionality
    $('.collapsible-header').click(function() {
        $(this).toggleClass('collapsed');
        var target = $(this).data('target');
        $(target).collapse('toggle');
    });

    // Badge removal functionality
    $('.badge-tag .fa-times').click(function() {
        $(this).parent().remove();
    });

    // Risk calculation functionality
    $(document).on('change', '.riskOccurrence, .riskSeverity', function() {
        var riskItem = $(this).closest('.risk-item');
        var occurrence = riskItem.find('.riskOccurrence').val();
        var severity = riskItem.find('.riskSeverity').val();
        var scoreElement = riskItem.find('.form-control-static span');

        if (occurrence && severity) {
            var sum = Number(occurrence) + Number(severity);
            var scoreText = '';
            var scoreClass = 'badge-secondary';

            if (sum <= 3) {
                scoreText = 'Low';
                scoreClass = 'badge-success';
            } else if (sum <= 5) {
                scoreText = 'Moderate';
                scoreClass = 'badge-warning';
            } else if (sum <= 6) {
                scoreText = 'High';
                scoreClass = 'badge-danger';
            } else {
                scoreText = 'Very High';
                scoreClass = 'badge-danger';
            }

            scoreElement.removeClass('badge-success badge-warning badge-danger badge-secondary')
                .addClass(scoreClass).text(scoreText);
        } else {
            scoreElement.removeClass('badge-success badge-warning badge-danger badge-secondary')
                .addClass('badge-secondary').text('Not calculated');
        }

        updateRisksTabel();
    });
});
</script>

<?php $this->load->view('template/footer'); ?>