<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once( dirname(__FILE__) . "/../../../vendor/autoload.php");
use OTPHP\TOTP;

require_once(dirname(__FILE__) . '/AuthApi.php');

class Checklist<PERSON>pi extends AuthApi {

  public function __construct()
	{
		parent::__construct();
		$this->load->helper(array('form','forms'));
		$this->load->helper('old_form');
    $this->load->model('form_model');
		$this->load->model('folder_model');
		$this->load->model('group_model');
	}

  public function list() {
    if ($this->validate()) {
      $res = [];
      $res['checklists'] = $this->form_model->get_all(['checklist']);
      if (!empty($res['checklists'])) {
        $res['pages'] = $this->form_model->get_form_pages_by_form(array_keys($res['checklists']));
      }

      return $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode($res));
    }
  }

  public function submissions($page_id) {
    if ($this->validate()) {
      $this->VALID_UUIDv4($page_id);
      
      $res = [];
      $res['submissions'] = $this->form_model->get_all_surveys($page_id);

      $this->load->model(array('user_model'));
      $this->users = $this->user_model->get_all();
      $checklist_users = [];
      foreach($this->users as $user_id => $user)
      {
        $checklist_users[$user_id] = $this->users[$user_id]->name;
      }
      $res['users'] = $checklist_users;
      
      return $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode($res));
    }
  }

  public function questions($page_id) {
    if ($this->validate()) {
      $this->VALID_UUIDv4($page_id);
      
      $res = [];
      $res['page'] = $this->form_model->get_page($page_id);
      if (empty($res['page'])) {
        return $this->output
          ->set_status_header(404)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Page not found']));
      }

      $res['form'] = $this->form_model->get($res['page']->form_id);
      if (empty($res['form']) || ($res['form']->company_id !== $this->auth_company_id && intval($res['form']->global) !== 1)) {
        return $this->output
          ->set_status_header(403)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Access denied']));
      }

      $questions = $this->form_model->get_question_structure($page_id);
      $res['questions'] = $questions['structure'];
      $res['options'] = $questions['options'];
      
      // Add users to user-type questions
      if (!empty($questions['field']['users'])) {
        $this->load->model('user_model');
        $users = $this->user_model->get_all();
        foreach($questions['field']['users'] as $user_question_id) {
          foreach($users as $user) {
            $dropdown = new stdClass();
            $dropdown->option_id = $user->user_id;
            $dropdown->name = $user->name;
            $res['options'][$user_question_id][$user->user_id] = $dropdown;
          }
        }
      }

      return $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode($res));
    }
  }

  public function submit($page_id) {
    if ($this->validate()) {
      $this->VALID_UUIDv4($page_id);
      
      $page = $this->form_model->get_page($page_id);
      if (empty($page)) {
        return $this->output
          ->set_status_header(404)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Page not found']));
      }

      $form = $this->form_model->get($page->form_id);
      if (empty($form) || ($form->company_id !== $this->auth_company_id && intval($form->global) !== 1)) {
        return $this->output
          ->set_status_header(403)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Access denied']));
      }

      $questions = $this->form_model->get_question_structure($page_id);

      $this->load->helper(array('form','forms'));
      $this->load->library('form_validation');
      $this->config->load('form_validation');
      
      // Save form data
      $new_checklist_id = UUIDv4();
      $success = $this->form_model->save_form(
        $form->survey_id,
        $new_checklist_id,
        $questions['all'],
        $questions['field']
      );

      if (!$success) {
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Failed to save checklist']));
      }

      // Create new survey record
      $date = $this->input->post('date');
      if (!isset($date)) $date = date('Y-m-d');
      $success = $this->form_model->save_surveys(
        $new_checklist_id,
        $page_id,
        $date
      );

      if (!$success) {
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Failed to create survey record']));
      }

      if( $date !== NULL)
			{
				$this->load->model('user_messages_model');
				$this->user_messages_model->remove_comment( 'checklist', $this->auth_user_id, $page_id, $date);				
			}

      return $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode(['success' => true]));
    }
  }

}
