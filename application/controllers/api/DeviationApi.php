<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once( dirname(__FILE__) . "/../../../vendor/autoload.php");
use OTPHP\TOTP;

require_once(dirname(__FILE__) . '/AuthApi.php');

class Deviation<PERSON><PERSON> extends AuthApi {

  public function __construct()
	{
		parent::__construct();
		$this->load->model(array('deviation_model'));
		$db = [
			'hostname' => $this->db->hostname,
			'database' => $this->db->database,
			'username' => $this->db->username,
			'password' => $this->db->password,
		];
		$this->load->library('deviationlib',$db);
		$this->load->helper(array('form','forms'));
		$this->load->helper('old_form');
    $this->load->model('group_model');
		$this->departments = '';
	}

  public function upload() {
    if ($this->validate(false)) {
      $a_id = $this->input->post('id');
      if (!$a_id || !VALID_UUIDv4($a_id)) {
        return $this->output
          ->set_status_header(400)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Invalid UUID']));
      }

      $upload_base_path = CI_UPLOAD_PATH . 'deviation';
      $dir_exists = true;
      $upload_data = array();
      
      if (!empty($_FILES['file']['name']) && $upload_base_path !== false) {
        $upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
        if (!is_dir($upload_path)) {
          $dir_exists = mkdir($upload_path, 0777);
        }

        if ($dir_exists) {
          $attachment_id = UUIDv4();
          $config = [
            'upload_path' => $upload_path,
            'allowed_types' => $this->config->item('allowed_types'),
            'file_name' => $attachment_id
          ];

          $this->load->library('upload', $config);
          $this->upload->initialize($config);

          if (!$this->upload->do_upload('file')) {
            return $this->output
              ->set_status_header(400)
              ->set_content_type('application/json')
              ->set_output(json_encode(['error' => $this->upload->display_errors('','')]));
          }

          $file_data = $this->upload->data();
          $upload_data = [
            'a_id' => UUID_TO_BIN($a_id),
            'attachment_id' => UUID_TO_BIN($attachment_id),
            'file_name' => $file_data['client_name'] ?: $file_data['orig_name'],
            'file_ext' => $file_data['file_ext'],
            'uploaded_on' => date("Y-m-d H:i:s")
          ];

          if (!empty($upload_data)) {
            if ($this->deviation_model->save_attachments($upload_data)) {
              return $this->output
                ->set_status_header(200)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                  'file_name' => $upload_data['file_name'],
                  'uploaded_on' => $upload_data['uploaded_on'],
                  'attachment_id' => $attachment_id
                ]));
            }
          }
        }
      }

      return $this->output
        ->set_status_header(400)
        ->set_content_type('application/json')
        ->set_output(json_encode(['error' => 'Unknown error']));
    }
  }

  public function getFields() {
    if ($this->validate()) {
      $page = $this->input->post('page');
      // Check if page is valid (1-3)
      if (!in_array($page, [1, 2, 3])) {
        return $this->output
          ->set_status_header(400)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Invalid page number']));
      }

      $this->data['rights'] = $this->acl['deviation'];
      // Check ACL permissions for page 2/3
      $departments = $this->deviation_model->deviationDepartment($this->input->post('id'));
      if (($page == 2 && !acl_deviation_permits('deviation_two', $departments)) || 
          ($page == 3 && !acl_deviation_permits('deviation_three', $departments))) {
        return $this->output
          ->set_status_header(403)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Access denied for this page']));
      }

      // Get fields and options for the requested page
      $fields = $this->deviation_model->getDeviationFieldsByPage($page);
      $options = $this->deviation_model->getDropdown();
      $this->load->model(array('user_model'));
      $this->data['groups']   = $this->group_model->get_all();
      $this->users = $this->user_model->get_all();
      $this->_setOptions($fields, $options);

      return $this->output
        ->set_status_header(200)
        ->set_content_type('application/json')
        ->set_output(json_encode([
          'fields' => $fields['all'],
          'options' => $options
        ]));
    }
  }

  public function add() {
    if ($this->validate()) {
      $a_id = $this->input->post('id');
      $page = $this->input->post('page');
      if (!isset($a_id)) {
        $a_id = UUIDv4();
        $page = 1;
      } else if( !VALID_UUIDv4($a_id) OR strlen($a_id) !== 36 ) {
        return $this->output
          ->set_status_header(400)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Invalid UUID format']));
      }

      // Validate page number
      if (!in_array($page, [1, 2, 3])) {
        return $this->output
          ->set_status_header(400)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Invalid page number']));
      }

      // Get fields for the page
      $fields = $this->deviation_model->getDeviationFieldsByPage($page);
      if (empty($fields)) {
        return $this->output
          ->set_status_header(400)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'No fields found for page']));
      }

      if( $a_id !== NULL)
		  {
        // Check ACL permissions for page 2/3
        $departments = $this->deviation_model->deviationDepartment($a_id);
        if (($page == 2 && !acl_deviation_permits('deviation_two', $departments)) || 
            ($page == 3 && !acl_deviation_permits('deviation_three', $departments))) {
          return $this->output
            ->set_status_header(403)
            ->set_content_type('application/json')
            ->set_output(json_encode(['error' => 'Access denied for this page']));
        }
      }

      $departments = $this->input->post('department');

      if ($page > 1) {
        $registered = $this->deviation_model->deviationRegistredBy($a_id);
        if( empty($registered) 
        || $page === 2 && ! empty($registered->regby_two) 
        || $page === 3 && ! empty($registered->regby_three) ) {
          return $this->output
            ->set_status_header(404)
            ->set_content_type('application/json')
            ->set_output(json_encode(['error' => 'Deviation already added']));
        }
      }


      // Save deviation data
      $this->users = $this->user_model->get_all();
      if ($this->deviation_model->save($a_id, $page) === FALSE) {
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Failed to save deviation']));
      }

      // Save answers
      if ($this->deviation_model->save_answers($a_id, $fields['all']) === FALSE) {
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Failed to save answers']));
      }

      // Save departments if page 1
      if ($this->deviation_model->save_departments($a_id) === FALSE) {
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Failed to save departments']));
      }

      // Save fields
      if ($this->deviation_model->save_fields($a_id, $fields['all']) === FALSE) {
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Failed to save fields']));
      }

      if( $address = $this->deviation_model->save_emails($a_id, $departments) ) {
				if( ! empty($address) )
				{
					$this->_send_email($address, $page, $a_id, $this->deviation_model->getDeviationName($a_id));
				}
			} else {
				$old_emails = $this->deviation_model->get_email($a_id);
				$old_address = array();
				foreach($old_emails as $field_id=>$old_users) {
					foreach($old_users as $user_id) {
						$old_address[$this->users[$user_id]->email] = $this->users[$user_id]->name;
					}
				}
				if( ! empty($old_address) )
				{
					$this->_send_email($old_address, $page, $a_id, $this->deviation_model->getDeviationName($a_id));
				}
			}

      return $this->output
        ->set_status_header(200)
        ->set_content_type('application/json')
        ->set_output(json_encode([
          'success' => true,
          'id' => $a_id,
          'page' => $page
        ]));
    }
  }

  public function search() {
    if ($this->validate()) {
      $departments = $this->acl['deviation']['read'] ?? [];
      if (empty($departments)) {
        return $this->output
          ->set_status_header(403)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'No access permissions']));
      }

      $this->_loadFields();
      $searchData = $this->deviation_model->search($departments);
      $heading_df_id = 0;
      $fields = $this->data['fields'];
      foreach($searchData['list'] as $df_id => $val) {
        $field = $fields['all'][$df_id]; 
        if ($field->input == 'input' && $field->required_kvalprak == 1) {
          $heading_df_id = $df_id;
          break;
        }		
      }
      return $this->output
        ->set_status_header(200)
        ->set_content_type('application/json')
        ->set_output(json_encode([
          'list' => $searchData['list'], // list of columns (fields) to show in the list
          'deviations' => $searchData['deviations'], // deviations list. Each record is an array of fields (that have answers)
          'fields' => $fields['all'], // All fields definitions (name of the field, type, etc..)
          'heading_df_id' => $heading_df_id
        ]));
    }
  }

  public function view( $id ) {
    if ($this->validate()) {
      if( !VALID_UUIDv4($id) OR strlen($id) !== 36 ) {
        return $this->output
          ->set_status_header(400)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Invalid UUID format']));
      }

      $this->data['rights'] = $this->acl['deviation'];
      if( empty($this->data['rights']['create']) ) {
        return $this->output
          ->set_status_header(403)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'No access permissions']));
      }

      $registered = $this->deviation_model->deviationRegistredBy($id);
      if( empty($registered) ) {
        return $this->output
          ->set_status_header(404)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'Deviation not found']));
      }

      $fields = $this->deviation_model->getDeviationFieldsByPage(array(1,2,3));
      if( empty($fields) ) {
        return $this->output
          ->set_status_header(404)
          ->set_content_type('application/json')
          ->set_output(json_encode(['error' => 'No fields found']));
      }

      $departments = $this->deviation_model->deviationDepartment($id);
      $attachments = $this->deviation_model->get_attachments($id);
      $options     = $this->deviation_model->getDropdown();
      $groups      = $this->group_model->get_all();
      $selected    = [];
      $this->load->model(array('user_model'));
      $this->users = $this->user_model->get_all();
      $this->_setOptions($fields, $options);
		  $this->_setSelected($fields, $selected, $departments, $id);

      $deviation = $this->deviation_model->getDeviationPageAndOrId($id);

      $heading = '';
      foreach($deviation as $key => $val) {
        if ($val->input == 'input' && $val->required_kvalprak == 1) {
          $heading = $val->answer;
          break;
        }
      }

      return $this->output
        ->set_status_header(200)
        ->set_content_type('application/json')
        ->set_output(json_encode([
          'id' => $id,
          'fields' => $deviation, 
          'attachments' => $attachments, 
          'options' => $options,
          'selected' => $selected, // if field type is radio or dropdown the selected of the df_id (deviation field id) would point to which id of the options is selected
          'rightsPage2' => acl_deviation_permits('deviation_two', $departments),
          'rightsPage3' => acl_deviation_permits('deviation_three', $departments)
        ]));
    }
  }

  private function _send_email($address, $page, $a_id, $title)
	{
		// Send email
		$this->data['a_id']          = $a_id;
		$this->data['title']         = $title;
		$this->data['page']          = $page;
		$this->data['created_by']    = $this->auth_name;
		$this->data['deviation_url'] = site_url('deviation/viewpage/1/' . $a_id);
		$this->data['completed_url'] = site_url('deviation/view/' . $a_id);

		$this->load->library('PHPMailerLib');
		$mail = $this->phpmailerlib->load();
		$mail->Subject = $this->config->item('program_name') . ': Avvikelserapport (sida ' . $page . ')';
		$mail->Body = $this->load->view('general/deviation/send_email',$this->data,TRUE);

		foreach($address as $email => $name)
		{
			$mail->addAddress($email,$name);
		}

		$mail->send();
	}

  private function _loadFields()
	{
		$this->data['fields']  = $this->deviation_model->getDeviationFieldsByPage(array(1,2,3));
		$this->data['groups']  = $this->group_model->get_all();
		$this->data['options'] = $this->deviation_model->getDropdown();
		$this->data['rights']  = $this->acl['deviation'];
    $this->users = $this->user_model->get_all();
		$this->_setOptions($this->data['fields'], $this->data['options']);		
	}

  private function _setSelected($fields, &$selected, $departments, $id)
	{
		if( ! empty($fields['type']['department']) )
		{
			foreach($fields['type']['department'] as $department)
			{
				$selected[$department] = $departments;
			}
		}

		$emails = $this->deviation_model->get_email($id);

		if( ! empty($this->data['fields']['type']['email']) && ! empty($emails) )
		{
			foreach($this->data['fields']['type']['email'] as $email)
			{
				if( isset($emails[$email]) )
				{
					foreach($emails[$email] as $user_id)
					{
						if( ! isset($this->users[$user_id]) )
							continue;

						$selected[$email][] = $this->users[$user_id]->name;
					}
				}
			}
		}

	}

}
