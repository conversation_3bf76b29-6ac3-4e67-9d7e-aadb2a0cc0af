<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once( dirname(__FILE__) . "/../../../vendor/autoload.php");
use OTPHP\TOTP;

class Auth<PERSON>pi extends User_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->helper('jwt');
        $this->load->database();
		$this->config->load('db_tables');
		$this->config->load('authentication');
		$this->load->library('authentication')->model('auth_model');
        $this->load->model('user_model');
    }

    public function login() {
        $_POST = json_decode($this->input->raw_input_stream, true);
        if ($this->authentication->login() === FALSE) {
            $this->output
                ->set_status_header(401)
                ->set_content_type('application/json')
                ->set_output(json_encode(['message' => 'Invalid credentials']));
            return;
        }

        // Get authenticated user data
        $user_email	= $this->input->post('login_email');
        $auth_data = $this->auth_model->get_auth_data( $user_email );
        if (!$auth_data) {
            $this->output
                ->set_status_header(401)
                ->set_content_type('application/json')
                ->set_output(json_encode(['message' => 'Authentication failed']));
            return;
        }

        if (!empty($auth_data->totp_secret)) {
            $otp = $this->input->post('otp');
            if (!isset($otp) || empty($otp)) {
                $this->output
                    ->set_status_header(401)
                    ->set_content_type('application/json')
                    ->set_output(json_encode(['message' => '2FA is required']));
                return;
            }
            if (!$this->validate_otp_internal($auth_data, $otp)) {
                $this->output
                    ->set_status_header(401)
                    ->set_content_type('application/json')
                    ->set_output(json_encode(['message' => 'Invalid 2FA OTP']));
                return;
            }
        }

        $user = (object) [
            'user_id' => $auth_data->user_id,
            'email' => $auth_data->email,
        ];

        $token = generate_jwt_token($user);
        $refresh_token = bin2hex(random_bytes(32));
        
        // Store hashed refresh token in database
        $refresh_token_expiry = date('Y-m-d H:i:s', time() + 86400 * 30); // 30 days
        $this->user_model->update_user_raw_data($auth_data->user_id, [
            'refresh_token' => $this->authentication->hash_password($refresh_token),
            'refresh_token_expiry' => $refresh_token_expiry
        ]);

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
            'refresh_token' => $refresh_token,
            'refresh_token_expiry' => $refresh_token_expiry,
            'name' => $auth_data->name,
            'email' => $auth_data->email,
            'token' => $token,
        ]));
    }

	protected function validate_otp_internal($auth_data, $otp_code) {
		$otp = TOTP::createFromSecret($auth_data->totp_secret); // create TOTP object from the secret.
		return $otp->verify($otp_code); // Returns true if the input is verified, otherwise false.
	}

    protected function validate($json = true) {
        $headers = apache_request_headers();
        if (isset($headers['Authorization'])) {
            $token = str_replace('Bearer ', '', $headers['Authorization']);
            $decodedToken = validate_jwt_token($token);
            if ($decodedToken !== FALSE) {
                if ($json)
                    $_POST = json_decode($this->input->raw_input_stream, true);
                $this->auth_identifiers = serialize([
                    'user_id'    => $decodedToken->data->user_id,
                    'login_time' => date('Y-m-d H:i:s', $decodedToken->iat)
                ]);
                $this->CI =& get_instance();
                // Step 1: Destroy the current session
                $this->CI->session->sess_destroy();

                // Step 2: Set the new session ID (this must be done before session_start())
                session_id( $decodedToken->data->session );
                setcookie('ksession', $decodedToken->data->session);

                // Step 3: Start a new session
                session_start();

                // Step 4: Re-initialize the CodeIgniter session library
                $this->load->library('session');
                $this->CI->session->set_userdata( 'auth_identifiers', $this->auth_identifiers );
                $this->authentication->__construct();
                $this->auth_data = $this->authentication->check_login();
                $this->_set_user_variables();
                return $decodedToken;
            } else {
                $this->output
                    ->set_status_header(401)
                    ->set_content_type('application/json')
                    ->set_output(json_encode(['message' => 'Invalid token']));
            }
        } else {
            $this->output
                ->set_status_header(401)
                ->set_content_type('application/json')
                ->set_output(json_encode(['message' => 'Authorization header missing']));
        }
    }

    public function refresh() {
        $_POST = json_decode($this->input->raw_input_stream, true);
        $refresh_token = $this->input->post('refresh_token');
        
        if (empty($refresh_token)) {
            $this->output
                ->set_status_header(400)
                ->set_content_type('application/json')
                ->set_output(json_encode(['message' => 'Refresh token required']));
            return;
        }
        $user_email		= $this->input->post('login_email');
        $auth_data = $this->auth_model->get_auth_data( $user_email );
        
        if (!$auth_data || empty($auth_data->refresh_token) || 
            strtotime($auth_data->refresh_token_expiry) < time()) {
            $this->output
                ->set_status_header(401)
                ->set_content_type('application/json')
                ->set_output(json_encode(['message' => 'Expired refresh token']));
            return;
        }

        if (!$this->authentication->check_password($auth_data->refresh_token, $refresh_token)) {
            $this->output
                ->set_status_header(401)
                ->set_content_type('application/json')
                ->set_output(json_encode(['message' => 'Invalid refresh token']));
            return;
        }

        $user = (object) [
            'user_id' => $auth_data->user_id,
            'email' => $auth_data->email,
        ];

        $token = generate_jwt_token($user);
        $new_refresh_token = bin2hex(random_bytes(32));
        
        // Update refresh token in database
        $refresh_token_expiry = date('Y-m-d H:i:s', time() + 86400 * 30); // 30 days
        $this->user_model->update_user_raw_data($auth_data->user_id, [
            'refresh_token' => $this->authentication->hash_password($new_refresh_token),
            'refresh_token_expiry' => $refresh_token_expiry
        ]);

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
            'refresh_token' => $new_refresh_token,
            'refresh_token_expiry' => $refresh_token_expiry,
            'token' => $token,
        ]));
    }
}
